package localscribe

import (
	"bytes"
	"encoding/binary"
	"log"

	"github.com/gordonklaus/portaudio"
)

type Recorder struct {
	stream *portaudio.Stream
	buffer []int16
}

func NewRecorder(cfg Config) (*Recorder, error) {
	buffer := make([]int16, cfg.FramesPerBuffer)

	stream, err := portaudio.OpenDefaultStream(1, 0, float64(cfg.SampleRate), cfg.FramesPerBuffer, buffer)
	if err != nil {
		return nil, err
	}

	return &Recorder{
		stream: stream,
		buffer: buffer,
	}, nil
}

func (r *Recorder) Read() ([]byte, error) {
	if err := r.stream.Read(); err != nil {
		return nil, err
	}

	var buf bytes.Buffer

	if err := binary.Write(&buf, binary.LittleEndian, r.buffer); err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

func (r *Recorder) Start() error {
	return r.stream.Start()
}

func (r *Recorder) Stop() error {
	return r.stream.Stop()
}

func (r *Recorder) Close() error {
	return r.stream.Close()
}

func cleanupRecorder(rec *Recorder) {
	log.Println("stopping voice recorder")
	rec.Stop()
	rec.Close()
}
