package localscribe

import (
	"context"
	"fmt"
	"log"
	"os"
	"path"
	"sync"
	"time"

	"github.com/AssemblyAI/assemblyai-go-sdk"
	"github.com/gordonklaus/portaudio"
)

// TranscriptionSession represents an active transcription session
type TranscriptionSession struct {
	ctx        context.Context
	cancel     context.CancelFunc
	config     LibraryConfig
	backend    TranscriptionBackend
	recorder   *Recorder
	running    bool
	mutex      sync.Mutex
	outputChan chan string // Channel for sending output to debug console
}

// OutputMessage represents a message to be sent to the debug console
type OutputMessage struct {
	Timestamp time.Time
	Source    string // "localscribe", "transcription", "error", etc.
	Message   string
	Level     string // "info", "warning", "error", "debug"
}

// LibraryConfig extends the basic Config with library-specific options
type LibraryConfig struct {
	Config
	OutputChannel chan OutputMessage // Channel to send debug messages
	LogToConsole  bool               // Whether to also log to console
}

// NewLibraryConfig creates a new library configuration with sensible defaults
func NewLibraryConfig(assemblyAIKey, logFile string, output<PERSON>han chan OutputMessage) LibraryConfig {
	if logFile == "" {
		defaultNowString := time.Now().Format("20060102_1504")
		logFile = path.Join(os.Getenv("HOME"), ".local", "scribe", "transcription-"+defaultNowString+".log")
	}

	return LibraryConfig{
		Config: Config{
			LogFile:         logFile,
			AssemblyAIKey:   assemblyAIKey,
			SampleRate:      16000,
			FramesPerBuffer: 3200,
			RESTPort:        8080,
			TranscriptOnly:  true, // Default to transcript-only mode for library usage
		},
		OutputChannel: outputChan,
		LogToConsole:  false, // Default to not logging to console in library mode
	}
}

// sendOutput sends a message to the output channel if available
func (s *TranscriptionSession) sendOutput(source, message, level string) {
	if s.config.OutputChannel != nil {
		select {
		case s.config.OutputChannel <- OutputMessage{
			Timestamp: time.Now(),
			Source:    source,
			Message:   message,
			Level:     level,
		}:
		default:
			// Channel is full, skip this message
		}
	}

	if s.config.LogToConsole {
		log.Printf("[%s] %s: %s", level, source, message)
	}
}

// NewTranscriptionSession creates a new transcription session
func NewTranscriptionSession(config LibraryConfig) (*TranscriptionSession, error) {
	if config.AssemblyAIKey == "" {
		return nil, fmt.Errorf("AssemblyAI API key is required")
	}

	ctx, cancel := context.WithCancel(context.Background())
	config.Config.Context = ctx

	session := &TranscriptionSession{
		ctx:        ctx,
		cancel:     cancel,
		config:     config,
		outputChan: make(chan string, 100),
	}

	session.sendOutput("localscribe", "Transcription session created", "info")
	return session, nil
}

// Start begins the transcription session
func (s *TranscriptionSession) Start() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.running {
		return fmt.Errorf("session is already running")
	}

	s.sendOutput("localscribe", "Starting transcription session", "info")

	// Ensure log directory exists
	logDir := path.Dir(s.config.LogFile)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("failed to create log directory: %v", err)
	}

	// Initialize PortAudio
	if err := portaudio.Initialize(); err != nil {
		return fmt.Errorf("failed to initialize PortAudio: %v", err)
	}

	// Create recorder
	recorder, err := NewRecorder(s.config.Config)
	if err != nil {
		portaudio.Terminate()
		return fmt.Errorf("failed to create recorder: %v", err)
	}
	s.recorder = recorder

	// Create transcription backend
	backend := s.createTranscriptionBackend()
	s.backend = backend

	// Connect to backend
	if err := backend.Connect(s.ctx); err != nil {
		s.recorder.Close()
		portaudio.Terminate()
		return fmt.Errorf("failed to connect to transcription backend: %v", err)
	}

	s.running = true
	s.sendOutput("localscribe", "Transcription session started successfully", "info")

	// Start transcription loop in a goroutine
	go s.runTranscriptionLoop()

	return nil
}

// Stop ends the transcription session
func (s *TranscriptionSession) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.running {
		return fmt.Errorf("session is not running")
	}

	s.sendOutput("localscribe", "Stopping transcription session", "info")

	// Cancel context to stop all operations
	s.cancel()

	// Close recorder
	if s.recorder != nil {
		s.recorder.Close()
	}

	// Disconnect backend
	if s.backend != nil {
		s.backend.Disconnect()
	}

	// Terminate PortAudio
	portaudio.Terminate()

	s.running = false
	s.sendOutput("localscribe", "Transcription session stopped", "info")

	return nil
}

// IsRunning returns whether the session is currently running
func (s *TranscriptionSession) IsRunning() bool {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	return s.running
}

// GetOutputChannel returns the output channel for receiving transcription text
func (s *TranscriptionSession) GetOutputChannel() <-chan string {
	return s.outputChan
}

// createTranscriptionBackend creates the appropriate transcription backend
func (s *TranscriptionSession) createTranscriptionBackend() TranscriptionBackend {
	s.sendOutput("localscribe", "Creating AssemblyAI transcription backend", "debug")

	transcriber := &assemblyai.RealTimeTranscriber{
		OnSessionBegins: func(event assemblyai.SessionBegins) {
			s.sendOutput("transcription", "Session begins", "info")
		},
		OnPartialTranscript: func(transcript assemblyai.PartialTranscript) {
			// Send partial transcripts to output channel
			select {
			case s.outputChan <- transcript.Text:
			default:
				// Channel is full, skip this partial transcript
			}
		},
		OnFinalTranscript: func(transcript assemblyai.FinalTranscript) {
			s.sendOutput("transcription", fmt.Sprintf("Final: %s", transcript.Text), "info")
			
			// Write to log file
			if err := WriteToLogFile(s.config.LogFile, transcript.Text); err != nil {
				s.sendOutput("localscribe", fmt.Sprintf("Failed to write to log file: %v", err), "error")
			}
		},
		OnError: func(err error) {
			s.sendOutput("transcription", fmt.Sprintf("Error: %v", err), "error")
		},
		OnSessionTerminated: func(event assemblyai.SessionTerminated) {
			s.sendOutput("transcription", "Session terminated", "info")
		},
	}

	client := assemblyai.NewRealTimeClientWithOptions(
		assemblyai.WithRealTimeAPIKey(s.config.AssemblyAIKey),
		assemblyai.WithRealTimeTranscriber(transcriber),
		assemblyai.WithRealTimeSampleRate(s.config.SampleRate),
	)

	return &AssemblyAIBackend{
		client:      client,
		transcriber: transcriber,
	}
}

// runTranscriptionLoop runs the main transcription loop
func (s *TranscriptionSession) runTranscriptionLoop() {
	s.sendOutput("localscribe", "Starting transcription loop", "debug")

	if err := StartTranscriptionLoop(s.ctx, s.backend, s.recorder); err != nil {
		s.sendOutput("localscribe", fmt.Sprintf("Transcription loop error: %v", err), "error")
	}

	s.sendOutput("localscribe", "Transcription loop ended", "debug")
}
