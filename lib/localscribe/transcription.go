package localscribe

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/AssemblyAI/assemblyai-go-sdk"
)

// TranscriptionBackend is an interface for sending audio data to a real-time
// transcription service and handling connect/disconnect.
type TranscriptionBackend interface {
	Connect(ctx context.Context) error
	Disconnect() error
	Send(ctx context.Context, data []byte) error
	KeepAlive(ctx context.Context) error
}

// AssemblyAIBackend is a concrete backend implementing TranscriptionBackend
// using the AssemblyAI Go SDK.
type AssemblyAIBackend struct {
	client      *assemblyai.RealTimeClient
	transcriber *assemblyai.RealTimeTranscriber
}

// Connect opens the WebSocket connection to AssemblyAI's real-time API.
func (a *AssemblyAIBackend) Connect(ctx context.Context) error {
	return a.client.Connect(ctx)
}

// Disconnect ends the WebSocket connection, optionally waiting for final transcripts.
func (a *AssemblyAIBackend) Disconnect() error {
	ctx := context.Background()
	return a.client.Disconnect(ctx, true)
}

// Send streams audio data to the real-time API.
func (a *AssemblyAIBackend) Send(ctx context.Context, data []byte) error {
	return a.client.Send(ctx, data)
}

func (a *AssemblyAIBackend) KeepAlive(ctx context.Context) error {
	return a.client.ForceEndUtterance(ctx)
}

// StartTranscriptionLoop handles the main microphone read/send loop.
// It assumes the backend is connected and we have a valid recorder.
func StartTranscriptionLoop(
	ctx context.Context,
	backend TranscriptionBackend,
	rec *Recorder, // from microphone.go logic
) error {
	if rec == nil {
		return errors.New("no recorder provided")
	}

	// Start capturing audio from the microphone
	if err := rec.Start(); err != nil {
		return fmt.Errorf("recorder start failed: %w", err)
	}
	defer cleanupRecorder(rec)

	for {
		select {
		case <-ctx.Done():
			// Context canceled (e.g., user hit Ctrl-C)
			return nil
		default:
			// Read audio samples from the microphone
			audioData, err := rec.Read()
			if err != nil {
				return fmt.Errorf("read from recorder failed: %w", err)
			}

			// If paused, skip sending
			if paused {
				backend.KeepAlive(ctx)
				time.Sleep(50 * time.Millisecond)
				continue
			}

			// Attempt to send data to the backend
			err = backend.Send(ctx, audioData)
			if err != nil {
				log.Printf("Warning: failed to send data to backend: %v", err)

				// Handle backend unavailability
				// Attempt to reconnect with exponential backoff
				if err := handleBackendReconnect(ctx, backend); err != nil {
					log.Printf("Error during backend reconnection: %v", err)
					// Depending on your preference, you can choose to continue or return
					// Here, we'll continue to allow further retries
					continue
				}

				// After reconnection, continue the loop to retry sending
				continue
			}
		}
	}
}

// handleBackendReconnect attempts to reconnect to the backend with exponential backoff
func handleBackendReconnect(ctx context.Context, backend TranscriptionBackend) error {
	maxRetries := 5
	baseDelay := time.Second

	for i := 0; i < maxRetries; i++ {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		delay := baseDelay * time.Duration(1<<uint(i)) // Exponential backoff
		log.Printf("Attempting to reconnect in %v (attempt %d/%d)", delay, i+1, maxRetries)
		time.Sleep(delay)

		if err := backend.Connect(ctx); err != nil {
			log.Printf("Reconnection attempt %d failed: %v", i+1, err)
			continue
		}

		log.Println("Successfully reconnected to backend")
		return nil
	}

	return fmt.Errorf("failed to reconnect after %d attempts", maxRetries)
}

// cleanupRecorder stops the recorder
func cleanupRecorder(rec *Recorder) {
	if rec != nil {
		rec.Stop()
	}
}
