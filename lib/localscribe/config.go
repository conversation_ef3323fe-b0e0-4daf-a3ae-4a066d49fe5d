package localscribe

import (
	"context"
	"fmt"
	"os"
	"time"
)

// Config holds all configuration for the localscribe system
type Config struct {
	LogFile         string
	AssemblyAIKey   string
	SampleRate      int
	FramesPerBuffer int
	Context         context.Context
	RESTPort        int
	TranscriptOnly  bool
}

var paused bool // Global pause state

// SetPaused sets the global pause state
func SetPaused(p bool) {
	paused = p
}

// IsPaused returns the current pause state
func IsPaused() bool {
	return paused
}

// TogglePaused toggles the global pause state
func TogglePaused() {
	paused = !paused
}

// atomicAppendToFile appends text to a file, creating it if necessary.
// it uses a simple semaphore locking mechanism to limit concurrency (block)
var lockSem = make(chan int, 1)

func AtomicAppendToFile(path, text string) error {
	lockSem <- 1 // acquire lock
	f, err := os.OpenFile(path, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = f.WriteString(text + "\n")
	<-lockSem // release lock
	return err
}

// WriteToLogFile writes transcription text to the log file with timestamp
func WriteToLogFile(logFile, text string) error {
	if text == "" {
		return nil
	}
	
	timestamp := getDateTime()
	line := fmt.Sprintf("%s %s", timestamp, text)
	return AtomicAppendToFile(logFile, line)
}

func ScribeIPInfo(cfg Config) {
	info := fetchIPInfo()
	line := fmt.Sprintf("%s %s - %s\n", getDateTime(), "%%% ipinfo", info)
	AtomicAppendToFile(cfg.LogFile, line)
}

// getDateTime returns the current local time formatted as specified.
func getDateTime() string {
	return time.Now().Format("2006/01/02 15:04:05 EST")
}
