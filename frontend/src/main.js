import './style.css';

// Lorecaster HUD Application Logic
class LorecasterHUD {
    constructor() {
        this.messages = [];
        this.logText = '';
        this.messageList = document.getElementById('message-list');
        this.statusElement = document.getElementById('session-status');
        this.clearBtn = document.getElementById('clear-btn');
        this.settingsBtn = document.getElementById('settings-btn');
        this.textCanvas = document.getElementById('text-canvas');
        this.clearLogBtn = document.getElementById('clear-log-btn');
        this.commandInput = document.getElementById('command-input');
        this.executeBtn = document.getElementById('execute-btn');
        this.startSessionBtn = document.getElementById('start-session-btn');
        this.stopSessionBtn = document.getElementById('stop-session-btn');
        this.textCanvasUpdateInterval = null;

        // Preferences modal elements
        this.preferencesModal = document.getElementById('preferences-modal');
        this.preferencesForm = document.getElementById('preferences-form');
        this.preferencesClose = document.getElementById('preferences-close');
        this.preferencesSave = document.getElementById('preferences-save');
        this.preferencesCancel = document.getElementById('preferences-cancel');
        this.dndVersionRadios = document.querySelectorAll('input[name="dnd-version"]');
        this.geminiApiKeyInput = document.getElementById('gemini-api-key');
        this.assemblyAIApiKeyInput = document.getElementById('assemblyai-api-key');

        // Debug console elements
        this.debugConsoleBtn = document.getElementById('debug-console-btn');
        this.debugConsoleModal = document.getElementById('debug-console-modal');
        this.debugConsoleClose = document.getElementById('debug-console-close');
        this.debugConsoleMessages = document.getElementById('debug-console-messages');
        this.debugMessageCount = document.getElementById('debug-message-count');
        this.debugAutoScroll = document.getElementById('debug-auto-scroll');
        this.debugFilterLevel = document.getElementById('debug-filter-level');

        this.debugMessages = [];
        this.debugMessageId = 0;

        this.sessionActive = false;
        this.lastContentLength = 0;

        // Timer elements and state
        this.timerDisplay = document.getElementById('timer-display');
        this.timerToggle = document.getElementById('timer-toggle');
        this.sessionStartTime = null;
        this.timerInterval = null;
        this.showElapsed = true; // true = elapsed, false = remaining
        this.sessionDuration = 4 * 60 * 60 * 1000; // 4 hours in milliseconds

        this.initializeEventListeners();
        this.updateStatus('Ready');

        // Add some sample messages for testing
        this.addSampleMessages();
        this.addSampleLogText();
    }

    initializeEventListeners() {
        this.clearBtn.addEventListener('click', () => this.clearMessages());
        this.debugConsoleBtn.addEventListener('click', () => this.showDebugConsole());
        this.settingsBtn.addEventListener('click', () => this.showSettings());
        this.clearLogBtn.addEventListener('click', () => this.clearLog());
        this.executeBtn.addEventListener('click', () => this.executeCommand());
        this.startSessionBtn.addEventListener('click', () => this.startSession());
        this.stopSessionBtn.addEventListener('click', () => this.stopSession());
        this.timerToggle.addEventListener('click', () => this.toggleTimerMode());

        // Debug console event listeners
        this.debugConsoleClose.addEventListener('click', () => this.closeDebugConsole());
        this.debugFilterLevel.addEventListener('change', () => this.filterDebugMessages());

        // Close debug console when clicking outside
        this.debugConsoleModal.addEventListener('click', (e) => {
            if (e.target === this.debugConsoleModal) {
                this.closeDebugConsole();
            }
        });

        // Preferences modal event listeners
        this.preferencesClose.addEventListener('click', (e) => {
            e.preventDefault();
            this.closePreferences();
        });
        this.preferencesSave.addEventListener('click', (e) => {
            e.preventDefault();
            this.savePreferences();
        });
        this.preferencesCancel.addEventListener('click', (e) => {
            e.preventDefault();
            this.closePreferences();
        });

        // Close modal when clicking outside
        this.preferencesModal.addEventListener('click', (e) => {
            if (e.target === this.preferencesModal) {
                this.closePreferences();
            }
        });

        // Handle API key masking on blur
        this.geminiApiKeyInput.addEventListener('blur', () => this.maskApiKeyInput(this.geminiApiKeyInput));
        this.assemblyAIApiKeyInput.addEventListener('blur', () => this.maskApiKeyInput(this.assemblyAIApiKeyInput));

        // Prevent form submission
        this.preferencesForm.addEventListener('submit', (e) => {
            e.preventDefault();
            return false;
        });

        // Handle Enter key in command input
        this.commandInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.executeCommand();
            }
        });
    }

    addMessage(type, content, imagePath = null) {
        const message = {
            id: Date.now(),
            type: type,
            content: content,
            imagePath: imagePath,
            timestamp: new Date()
        };

        this.messages.unshift(message); // Add to beginning
        this.renderMessage(message);
        this.updateStatus(`${this.messages.length} messages`);
    }

    renderMessage(message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message-item';
        messageElement.dataset.messageId = message.id;

        messageElement.innerHTML = `
            <div class="message-header">
                <span class="message-type">${message.type}</span>
                <span class="message-time">${this.formatTime(message.timestamp)}</span>
            </div>
            <div class="message-content">${message.content}</div>
            <div class="message-actions">
                <button class="action-btn gallery" onclick="hud.sendToGallery(${message.id})">📸 Gallery</button>
                <button class="action-btn log" onclick="hud.logMessage(${message.id})">📝 Log</button>
            </div>
        `;

        // Insert at the beginning of the message list
        this.messageList.insertBefore(messageElement, this.messageList.firstChild);
    }

    formatTime(date) {
        return date.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    clearMessages() {
        this.messages = [];
        this.messageList.innerHTML = '';
        this.updateStatus('Messages cleared');
    }

    sendToGallery(messageId) {
        const message = this.messages.find(m => m.id === messageId);
        if (message) {
            console.log('Sending to gallery:', message);
            this.updateStatus(`Sent "${message.type}" to gallery`);
            // TODO: Implement gallery integration
        }
    }

    logMessage(messageId) {
        const message = this.messages.find(m => m.id === messageId);
        if (message) {
            console.log('Logging message:', message);
            this.updateStatus(`Logged "${message.type}" message`);
            // TODO: Implement logging functionality
        }
    }

    async showSettings() {
        try {
            // For now, just show the modal with default values
            // TODO: Load current settings from backend when implemented
            this.preferencesModal.classList.remove('hidden');
        } catch (error) {
            console.error('Failed to load settings:', error);
            this.updateStatus('Failed to load settings');
        }
    }

    closePreferences() {
        this.preferencesModal.classList.add('hidden');
    }

    async savePreferences() {
        try {
            const settings = this.getPreferencesFromForm();
            // TODO: Implement backend settings save when backend is ready
            console.log('Saving settings:', settings);
            this.updateStatus('Settings saved');
            this.closePreferences();
        } catch (error) {
            console.error('Failed to save settings:', error);
            this.updateStatus('Failed to save settings');
        }
    }

    getPreferencesFromForm() {
        // Get selected D&D version
        const selectedVersion = document.querySelector('input[name="dnd-version"]:checked');
        const dndVersion = selectedVersion ? selectedVersion.value : '2024';

        // Get Gemini API key
        let geminiAPIKey = this.geminiApiKeyInput.value.trim();

        // Get AssemblyAI API key
        let assemblyAIKey = this.assemblyAIApiKeyInput.value.trim();

        return {
            dndVersion: dndVersion,
            geminiAPIKey: geminiAPIKey,
            assemblyAIKey: assemblyAIKey
        };
    }

    maskApiKeyInput(inputElement) {
        const value = inputElement.value.trim();
        if (value && !value.includes('*') && value.length > 7) {
            // Only mask if it's a new value that's not already masked
            // Preserve length by filling middle with appropriate number of asterisks
            const middleLength = value.length - 7; // Total length minus first 3 and last 4
            const masked = value.substring(0, 3) + '*'.repeat(middleLength) + value.substring(value.length - 4);
            inputElement.value = masked;
            inputElement.dataset.originalValue = value; // Store original for saving
        } else if (value && !value.includes('*') && value.length <= 7) {
            // For short keys, mask the entire length
            const masked = '*'.repeat(value.length);
            inputElement.value = masked;
            inputElement.dataset.originalValue = value; // Store original for saving
        }
    }

    // Text Canvas Methods
    appendToLog(text) {
        this.logText += text + '\n';
        this.textCanvas.textContent = this.logText;
        // Auto-scroll to bottom
        this.textCanvas.scrollTop = this.textCanvas.scrollHeight;
    }

    async clearLog() {
        try {
            // TODO: Clear the backend buffer when implemented
            // Clear the frontend display
            this.logText = '';
            this.textCanvas.textContent = '';
            this.updateStatus('Log cleared');
        } catch (error) {
            console.error('Failed to clear log:', error);
            this.updateStatus('Failed to clear log');
        }
    }

    // Command execution
    async executeCommand() {
        const command = this.commandInput.value.trim();
        if (!command) return;

        this.commandInput.value = '';
        this.addDebugMessage('command', `Executing: ${command}`, 'info');

        const lowerCommand = command.toLowerCase();
        if (lowerCommand.startsWith('search ')) {
            const searchTerm = command.substring(7);
            await this.searchLog(searchTerm);
        } else if (lowerCommand === 'clear') {
            this.clearLog();
        } else if (lowerCommand === 'stats') {
            await this.showStats();
        } else if (lowerCommand === 'help') {
            await this.showHelp();
        } else {
            this.addDebugMessage('command', `Unknown command: "${command}"`, 'warning');
        }
    }

    async searchLog(searchTerm) {
        // Simple search in current log text
        const lines = this.logText.split('\n');
        const matches = lines.filter(line =>
            line.toLowerCase().includes(searchTerm.toLowerCase())
        );

        if (matches.length > 0) {
            this.addDebugMessage('search', `Found ${matches.length} matches for "${searchTerm}"`, 'info');
            // TODO: Highlight matches in the log
        } else {
            this.addDebugMessage('search', `No matches found for "${searchTerm}"`, 'info');
        }
    }

    async showStats() {
        const stats = {
            messages: this.messages.length,
            logLines: this.logText.split('\n').length,
            sessionActive: this.sessionActive,
            debugMessages: this.debugMessages.length
        };

        this.addDebugMessage('stats', `Messages: ${stats.messages}, Log lines: ${stats.logLines}, Session: ${stats.sessionActive ? 'Active' : 'Inactive'}, Debug: ${stats.debugMessages}`, 'info');
    }

    async showHelp() {
        const helpText = 'Available commands: search <term>, clear, stats, help';
        this.addDebugMessage('help', helpText, 'info');
    }

    // Session Control Methods
    async startSession() {
        this.updateStatus('Starting session...');
        this.startSessionBtn.disabled = true;

        try {
            // TODO: Call the Go backend to start localscribe library when implemented
            console.log('Session started');
            this.addDebugMessage('lorecaster', 'Session started', 'info');
            this.updateStatus('Session active');
            this.startSessionBtn.disabled = true;
            this.stopSessionBtn.disabled = false;

            // Update session state and start timer
            this.sessionActive = true;
            this.startTimer();

            // Add sample transcription text
            this.appendToLog('[Session Started]');
            this.appendToLog('Transcription will appear here...');
        } catch (error) {
            console.error(`Failed to start session: ${error}`);
            this.addDebugMessage('lorecaster', `Failed to start session: ${error}`, 'error');
            this.updateStatus('Start failed');
            this.startSessionBtn.disabled = false;
        }
    }

    async stopSession() {
        this.updateStatus('Stopping session...');
        this.stopSessionBtn.disabled = true;

        try {
            // TODO: Call the Go backend to stop localscribe library when implemented
            console.log('Session stopped');
            this.addDebugMessage('lorecaster', 'Session stopped', 'info');
            this.updateStatus('Ready');
            this.startSessionBtn.disabled = false;
            this.stopSessionBtn.disabled = true;

            // Update session state and stop timer
            this.sessionActive = false;
            this.stopTimer();

            this.appendToLog('[Session Stopped]');
        } catch (error) {
            console.error(`Failed to stop session: ${error}`);
            this.addDebugMessage('lorecaster', `Failed to stop session: ${error}`, 'error');
            this.updateStatus('Stop failed');
            this.stopSessionBtn.disabled = false;
        }
    }

    // Timer Methods
    startTimer() {
        this.sessionStartTime = Date.now();
        this.timerInterval = setInterval(() => this.updateTimer(), 1000);
        this.updateTimer();
    }

    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }

    updateTimer() {
        if (!this.sessionStartTime) return;

        const now = Date.now();
        const elapsed = now - this.sessionStartTime;

        let displayTime;
        if (this.showElapsed) {
            displayTime = elapsed;
        } else {
            displayTime = Math.max(0, this.sessionDuration - elapsed);
        }

        const hours = Math.floor(displayTime / (1000 * 60 * 60));
        const minutes = Math.floor((displayTime % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((displayTime % (1000 * 60)) / 1000);

        this.timerDisplay.textContent =
            `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        // Update timer color based on progress
        const progress = elapsed / this.sessionDuration;
        this.timerDisplay.classList.remove('warning', 'danger');

        if (progress >= 0.875) { // 87.5% = 1 hour remaining in 4-hour session
            this.timerDisplay.classList.add('danger');
        } else if (progress >= 0.5) { // 50% = halfway point
            this.timerDisplay.classList.add('warning');
        }
    }

    toggleTimerMode() {
        this.showElapsed = !this.showElapsed;
        this.timerToggle.textContent = this.showElapsed ? 'Elapsed' : 'Remaining';
        this.updateTimer();
    }

    // Debug Console Methods
    showDebugConsole() {
        this.debugConsoleModal.classList.remove('hidden');
        this.renderDebugMessages();
    }

    closeDebugConsole() {
        this.debugConsoleModal.classList.add('hidden');
    }

    addDebugMessage(source, message, level = 'info') {
        const debugMessage = {
            id: ++this.debugMessageId,
            timestamp: new Date(),
            source: source,
            message: message,
            level: level
        };

        this.debugMessages.push(debugMessage);
        this.updateDebugMessageCount();

        // Auto-render if console is open
        if (!this.debugConsoleModal.classList.contains('hidden')) {
            this.renderDebugMessages();
        }
    }

    renderDebugMessages() {
        const filterLevel = this.debugFilterLevel.value;
        const filteredMessages = filterLevel === 'all'
            ? this.debugMessages
            : this.debugMessages.filter(msg => msg.level === filterLevel);

        this.debugConsoleMessages.innerHTML = filteredMessages.map(msg => `
            <div class="debug-message ${msg.level}">
                <div class="debug-message-header">
                    <span class="debug-message-source">${msg.source}</span>
                    <span class="debug-message-timestamp">${this.formatTime(msg.timestamp)}</span>
                </div>
                <div class="debug-message-content">${msg.message}</div>
            </div>
        `).join('');

        // Auto-scroll if enabled
        if (this.debugAutoScroll.checked) {
            this.debugConsoleMessages.scrollTop = this.debugConsoleMessages.scrollHeight;
        }
    }

    filterDebugMessages() {
        this.renderDebugMessages();
    }

    updateDebugMessageCount() {
        this.debugMessageCount.textContent = `${this.debugMessages.length} messages`;
    }

    // Sample data for testing
    addSampleMessages() {
        this.addMessage('NPC', 'The tavern keeper looks up from cleaning a mug and nods in your direction.');
        this.addMessage('Combat', 'Roll initiative! The goblins have spotted you.');
        this.addMessage('Lore', 'This ancient rune speaks of a forgotten kingdom beneath the mountains.');
    }

    addSampleLogText() {
        this.appendToLog('[System] Lorecaster initialized');
        this.appendToLog('[Info] Ready for transcription session');
    }

    updateStatus(text) {
        this.statusElement.textContent = text;
        setTimeout(() => {
            if (!this.sessionActive) {
                this.statusElement.textContent = 'Ready';
            }
        }, 3000);
    }
}

// Initialize the application
const hud = new LorecasterHUD();
window.hud = hud; // Make it globally accessible for onclick handlers
