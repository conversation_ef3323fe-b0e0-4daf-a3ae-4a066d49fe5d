<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>Lorecaster - D<PERSON> Tools</title>
</head>
<body>
<div id="app">
    <div id="hud-container">
        <div id="hud-header">
            <h2>Lorecaster</h2>
            <div id="hud-controls">
                <button id="clear-btn" title="Clear all entries">🗑️</button>
                <button id="debug-console-btn" title="Debug Console">🐛</button>
                <button id="settings-btn" title="Settings">⚙️</button>
            </div>
        </div>
        <div id="hud-main">
            <!-- Left side: 60% - Text Canvas for localscribe logging -->
            <div id="text-canvas-panel">
                <div id="text-canvas-header">
                    <h3>Localscribe Log</h3>
                    <button id="clear-log-btn" title="Clear log">🗑️</button>
                </div>
                <div id="text-canvas">
                    <!-- Localscribe text will be piped here -->
                </div>
            </div>

            <!-- Right side: 40% - Widget streams and command area -->
            <div id="right-panel">
                <!-- Session Timer -->
                <div id="session-timer">
                    <div id="timer-display">00:00:00</div>
                    <button id="timer-toggle" class="timer-btn">Elapsed</button>
                </div>

                <!-- Widget streams -->
                <div id="widget-streams">
                    <div id="message-list">
                        <!-- Messages will be dynamically added here -->
                    </div>
                </div>

                <!-- Bottom 10% of right panel: Command area -->
                <div id="command-area">
                    <input type="text" id="command-input" placeholder="Enter command or search..." />
                    <button id="execute-btn" title="Execute">▶</button>
                </div>
            </div>
        </div>
        <div id="hud-footer">
            <div id="session-controls">
                <button id="start-session-btn" class="session-btn start">▶ Start Session</button>
                <button id="stop-session-btn" class="session-btn stop" disabled>⏹ Stop Session</button>
                <div id="session-status">Ready</div>
            </div>
        </div>
    </div>

    <!-- Debug Console Modal -->
    <div id="debug-console-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Debug Console</h3>
                <button id="debug-console-close" class="modal-close">×</button>
            </div>
            <div class="modal-body">
                <div id="debug-console-messages">
                    <!-- Debug messages will be displayed here -->
                </div>
            </div>
            <div class="modal-footer">
                <div class="debug-console-status">
                    <span id="debug-message-count">0 messages</span>
                    <label>
                        <input type="checkbox" id="debug-auto-scroll" checked> Auto-scroll
                    </label>
                    <label>
                        Filter:
                        <select id="debug-filter-level">
                            <option value="all">All</option>
                            <option value="error">Errors</option>
                            <option value="warning">Warnings</option>
                            <option value="info">Info</option>
                            <option value="debug">Debug</option>
                        </select>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- Preferences Modal -->
    <div id="preferences-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Preferences</h3>
                <button id="preferences-close" class="modal-close">×</button>
            </div>
            <div class="modal-body">
                <form id="preferences-form">
                    <div class="form-group">
                        <label>D&D Version:</label>
                        <div class="radio-group">
                            <label>
                                <input type="radio" name="dnd-version" value="2014" checked>
                                D&D 5e (2014)
                            </label>
                            <label>
                                <input type="radio" name="dnd-version" value="2024">
                                D&D 5e (2024)
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="gemini-api-key">Gemini API Key:</label>
                        <input type="password" id="gemini-api-key" placeholder="Enter your Gemini API key">
                        <small>Used for AI-powered content generation</small>
                    </div>
                    <div class="form-group">
                        <label for="assemblyai-api-key">AssemblyAI API Key:</label>
                        <input type="password" id="assemblyai-api-key" placeholder="Enter your AssemblyAI API key">
                        <small>Required for real-time transcription</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button id="preferences-save" class="btn btn-primary">Save</button>
                <button id="preferences-cancel" class="btn btn-secondary">Cancel</button>
            </div>
        </div>
    </div>
</div>
<script src="./src/main.js" type="module"></script>
</body>
</html>
