package main

import (
	"context"
	"encoding/json"
	"fmt"
	"lorecaster/lib/localscribe"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// HUDMessage represents a message displayed in the HUD
type HUDMessage struct {
	ID        int64     `json:"id"`
	Type      string    `json:"type"`
	Content   string    `json:"content"`
	ImagePath string    `json:"imagePath,omitempty"`
	Timestamp time.Time `json:"timestamp"`
}

// AppSettings represents the application settings/preferences
type AppSettings struct {
	DNDVersion    string `json:"dndVersion"`    // "2014" or "2024"
	GeminiAPIKey  string `json:"geminiAPIKey"`  // Stored securely, masked in UI
	AssemblyAIKey string `json:"assemblyAIKey"` // Stored securely, masked in UI
}

// ConsoleMessage represents a debug console message
type ConsoleMessage struct {
	ID        int64     `json:"id"`
	Timestamp time.Time `json:"timestamp"`
	Source    string    `json:"source"`
	Message   string    `json:"message"`
	Level     string    `json:"level"`
}

// App struct
type App struct {
	ctx                context.Context
	messages           []HUDMessage
	isVisible          bool
	localscribeRunning bool
	localscribeMutex   sync.Mutex
	textCanvasBuffer   []string
	textCanvasMutex    sync.Mutex
	settings           AppSettings
	settingsMutex      sync.Mutex

	// Console system for debug messages
	consoleMessages   []ConsoleMessage
	consoleMutex      sync.Mutex
	consoleMessageID  int64
	consoleOutputChan chan localscribe.OutputMessage

	// Localscribe library integration
	transcriptionSession *localscribe.TranscriptionSession
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{
		messages:          make([]HUDMessage, 0),
		isVisible:         true,
		textCanvasBuffer:  make([]string, 0),
		consoleMessages:   make([]ConsoleMessage, 0),
		consoleOutputChan: make(chan localscribe.OutputMessage, 100),
		settings: AppSettings{
			DNDVersion:    "2024", // Default to 2024 edition
			GeminiAPIKey:  os.Getenv("GEMINI_API_KEY"),
			AssemblyAIKey: os.Getenv("ASSEMBLYAI_API_KEY"),
		},
	}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
	fmt.Println("Lorecaster started")

	// Load settings from file
	if err := a.loadSettings(); err != nil {
		a.AddConsoleMessage("settings", fmt.Sprintf("Failed to load settings: %v", err), "warning")
		fmt.Printf("Failed to load settings: %v\n", err)
	} else {
		a.AddConsoleMessage("settings", "Settings loaded successfully", "info")
	}

	// Initialize console with startup messages
	a.AddConsoleMessage("lorecaster", "Lorecaster application started", "info")
	a.AddConsoleMessage("system", "Debug console initialized", "debug")

	// Start processing console output from localscribe
	go a.processConsoleOutput()
}

// AddConsoleMessage adds a message to the debug console
func (a *App) AddConsoleMessage(source, message, level string) {
	a.consoleMutex.Lock()
	defer a.consoleMutex.Unlock()

	a.consoleMessageID++
	consoleMsg := ConsoleMessage{
		ID:        a.consoleMessageID,
		Timestamp: time.Now(),
		Source:    source,
		Message:   message,
		Level:     level,
	}

	a.consoleMessages = append(a.consoleMessages, consoleMsg)

	// Keep only the last 1000 messages to prevent memory issues
	if len(a.consoleMessages) > 1000 {
		a.consoleMessages = a.consoleMessages[len(a.consoleMessages)-1000:]
	}
}

// processConsoleOutput processes output messages from localscribe
func (a *App) processConsoleOutput() {
	for msg := range a.consoleOutputChan {
		a.AddConsoleMessage(msg.Source, msg.Message, msg.Level)
	}
}

// StartLocalscribeLibrary starts localscribe using the integrated library
func (a *App) StartLocalscribeLibrary() string {
	a.localscribeMutex.Lock()
	defer a.localscribeMutex.Unlock()

	if a.localscribeRunning {
		return "Localscribe is already running"
	}

	// Check if AssemblyAI API key is available
	a.settingsMutex.Lock()
	assemblyAIKey := a.settings.AssemblyAIKey
	a.settingsMutex.Unlock()

	if assemblyAIKey == "" {
		a.AddConsoleMessage("localscribe", "AssemblyAI API key is required", "error")
		return "AssemblyAI API key is required. Please set it in Preferences first."
	}

	// Create a specific log file for this session
	logFilePath, err := a.createSessionLogFile()
	if err != nil {
		a.AddConsoleMessage("localscribe", fmt.Sprintf("Failed to create log file: %v", err), "error")
		return fmt.Sprintf("Failed to create log file: %v", err)
	}

	a.AddConsoleMessage("localscribe", fmt.Sprintf("Creating transcription session with log file: %s", logFilePath), "info")

	// Create library configuration
	config := localscribe.NewLibraryConfig(assemblyAIKey, logFilePath, a.consoleOutputChan)
	config.LogToConsole = false // We handle console output through our debug system

	// Create transcription session
	session, err := localscribe.NewTranscriptionSession(config)
	if err != nil {
		a.AddConsoleMessage("localscribe", fmt.Sprintf("Failed to create transcription session: %v", err), "error")
		return fmt.Sprintf("Failed to create transcription session: %v", err)
	}

	// Start the session
	if err := session.Start(); err != nil {
		a.AddConsoleMessage("localscribe", fmt.Sprintf("Failed to start transcription session: %v", err), "error")
		return fmt.Sprintf("Failed to start transcription session: %v", err)
	}

	a.transcriptionSession = session
	a.localscribeRunning = true

	a.AddConsoleMessage("localscribe", "Transcription session started successfully", "info")

	// Start processing transcription output
	go a.processTranscriptionOutput()

	return "Localscribe library started successfully"
}

// StopLocalscribeLibrary stops the localscribe library session
func (a *App) StopLocalscribeLibrary() string {
	a.localscribeMutex.Lock()
	defer a.localscribeMutex.Unlock()

	if !a.localscribeRunning || a.transcriptionSession == nil {
		return "Localscribe library is not running"
	}

	a.AddConsoleMessage("localscribe", "Stopping transcription session", "info")

	if err := a.transcriptionSession.Stop(); err != nil {
		a.AddConsoleMessage("localscribe", fmt.Sprintf("Error stopping transcription session: %v", err), "warning")
	}

	a.transcriptionSession = nil
	a.localscribeRunning = false

	a.AddConsoleMessage("localscribe", "Transcription session stopped", "info")
	return "Localscribe library stopped"
}

// processTranscriptionOutput processes transcription output from the library
func (a *App) processTranscriptionOutput() {
	if a.transcriptionSession == nil {
		return
	}

	outputChan := a.transcriptionSession.GetOutputChannel()
	for transcript := range outputChan {
		// Add transcript to text canvas
		a.sendToTextCanvas(transcript)
	}
}

// sendToTextCanvas sends a line of text to the frontend text canvas (internal method)
func (a *App) sendToTextCanvas(text string) {
	a.textCanvasMutex.Lock()
	defer a.textCanvasMutex.Unlock()

	// Add timestamp
	timestamp := time.Now().Format("15:04:05")
	line := fmt.Sprintf("[%s] %s", timestamp, text)

	a.textCanvasBuffer = append(a.textCanvasBuffer, line)

	// Keep only the last 1000 lines to prevent memory issues
	if len(a.textCanvasBuffer) > 1000 {
		a.textCanvasBuffer = a.textCanvasBuffer[len(a.textCanvasBuffer)-1000:]
	}
}

// createSessionLogFile creates a log file for the current session
func (a *App) createSessionLogFile() (string, error) {
	// Create log directory
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get home directory: %v", err)
	}

	logDir := filepath.Join(homeDir, ".local", "lorecaster")
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create log directory: %v", err)
	}

	// Create log file with timestamp
	timestamp := time.Now().Format("20060102_1504")
	logFile := filepath.Join(logDir, fmt.Sprintf("transcription-%s.log", timestamp))

	return logFile, nil
}

// Settings management methods
func (a *App) loadSettings() error {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("failed to get home directory: %v", err)
	}

	configDir := filepath.Join(homeDir, ".config", "lorecaster")
	settingsFile := filepath.Join(configDir, "settings.json")

	// Check if settings file exists
	if _, err := os.Stat(settingsFile); os.IsNotExist(err) {
		// File doesn't exist, use defaults
		return nil
	}

	data, err := os.ReadFile(settingsFile)
	if err != nil {
		return fmt.Errorf("failed to read settings file: %v", err)
	}

	a.settingsMutex.Lock()
	defer a.settingsMutex.Unlock()

	if err := json.Unmarshal(data, &a.settings); err != nil {
		return fmt.Errorf("failed to parse settings: %v", err)
	}

	return nil
}

func (a *App) saveSettings() error {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("failed to get home directory: %v", err)
	}

	configDir := filepath.Join(homeDir, ".config", "lorecaster")
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %v", err)
	}

	settingsFile := filepath.Join(configDir, "settings.json")

	a.settingsMutex.Lock()
	data, err := json.MarshalIndent(a.settings, "", "  ")
	a.settingsMutex.Unlock()

	if err != nil {
		return fmt.Errorf("failed to marshal settings: %v", err)
	}

	if err := os.WriteFile(settingsFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write settings file: %v", err)
	}

	return nil
}

// GetSettings returns the current settings (for frontend)
func (a *App) GetSettings() AppSettings {
	a.settingsMutex.Lock()
	defer a.settingsMutex.Unlock()

	// Return a copy with masked API keys
	settings := a.settings
	if settings.GeminiAPIKey != "" {
		settings.GeminiAPIKey = a.maskAPIKey(settings.GeminiAPIKey)
	}
	if settings.AssemblyAIKey != "" {
		settings.AssemblyAIKey = a.maskAPIKey(settings.AssemblyAIKey)
	}

	return settings
}

// SaveSettings saves the provided settings
func (a *App) SaveSettings(settings AppSettings) string {
	a.settingsMutex.Lock()
	defer a.settingsMutex.Unlock()

	// Handle API key updates (don't overwrite if masked)
	if settings.GeminiAPIKey != "" && !strings.Contains(settings.GeminiAPIKey, "*") {
		a.settings.GeminiAPIKey = settings.GeminiAPIKey
	}
	if settings.AssemblyAIKey != "" && !strings.Contains(settings.AssemblyAIKey, "*") {
		a.settings.AssemblyAIKey = settings.AssemblyAIKey
	}

	// Update other settings
	a.settings.DNDVersion = settings.DNDVersion

	if err := a.saveSettings(); err != nil {
		return fmt.Sprintf("Failed to save settings: %v", err)
	}

	a.AddConsoleMessage("settings", "Settings saved successfully", "info")
	return "Settings saved successfully"
}

// maskAPIKey masks an API key for display
func (a *App) maskAPIKey(key string) string {
	if len(key) <= 7 {
		return strings.Repeat("*", len(key))
	}
	// Show first 3 and last 4 characters, mask the middle
	middleLength := len(key) - 7
	return key[:3] + strings.Repeat("*", middleLength) + key[len(key)-4:]
}
